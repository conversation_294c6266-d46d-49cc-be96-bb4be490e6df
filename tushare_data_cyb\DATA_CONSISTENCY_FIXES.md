# 数据一致性修复报告

## 修复概述
本次修复解决了 `calculate_stock_factors_cyb.py` 文件中所有可能导致数据不一致的问题，确保无论使用3年数据还是半年数据，计算出的因子都能保持一致性。

## 修复的主要问题

### 1. 可变窗口大小问题
**问题**: 多个函数使用 `window*2`、`window*3` 等可变窗口大小，导致不同历史数据长度产生不同结果。

**修复**:
- `calculate_normalized_composite_momentum`: 将 `window*3` 改为固定的 `NORMALIZE_WINDOW = 60`
- `calculate_stealth_accumulation`: 将 `window*2` 改为固定的 `STEALTH_WINDOW = 40`
- `calculate_information_ratio`: 将 `window*3` 改为固定的 `INFO_RATIO_BENCHMARK_WINDOW = 60`
- `calculate_greed_index_proxy`: 将 `window*2` 改为固定的 `GREED_VOLUME_WINDOW = 40`

### 2. 累积计算问题
**问题**: `calculate_volume_price_trend` 函数使用 `cumsum()` 进行累积计算，导致历史数据长度直接影响结果。

**修复**: 已经修复为使用滚动窗口计算，避免累积依赖。

### 3. 常量定义标准化
**新增常量**:
```python
NORMALIZE_WINDOW = 60  # 标准化窗口
STEALTH_WINDOW = 40    # 隐形吸筹窗口
GREED_VOLUME_WINDOW = 40  # 贪婪指数成交量窗口
INFO_RATIO_BENCHMARK_WINDOW = 60  # 信息比率基准窗口
```

### 4. 最大窗口计算更新
更新了 `max_window` 计算，包含所有新的固定窗口常量，确保增量更新时有足够的历史数据。

## 验证的稳定函数

### 已确认稳定的函数类型:
1. **固定窗口函数**: 使用固定参数的滚动窗口计算
2. **分位数函数**: `calculate_dynamic_support_resistance` 使用固定20天窗口的分位数计算
3. **技术指标函数**: RSI, MACD, ATR 等使用固定参数的技术指标
4. **基础统计函数**: 移动平均、标准差等使用固定窗口的统计计算

### 已确认无问题的计算:
- 所有使用 `talib` 库的技术指标计算
- 固定窗口的滚动统计计算
- 基于固定参数的数学运算
- 数据预处理和清洗逻辑

## 数据一致性保证

### 修复后的保证:
1. **窗口一致性**: 所有滚动窗口计算使用固定大小，不依赖历史数据长度
2. **参数固定**: 所有关键参数都定义为常量，避免动态计算
3. **无累积依赖**: 移除了所有依赖全部历史数据的累积计算
4. **缓冲区充足**: 增量更新时提供足够的历史数据缓冲区

### 测试建议:
1. 使用相同的最近半年数据，分别从3年数据和半年数据中计算因子
2. 对比相同日期的因子值，应该完全一致
3. 验证模型推理结果的一致性

## 注意事项

### 仍需注意的地方:
1. **数据预处理**: 确保训练和推理时使用相同的数据预处理逻辑
2. **标准化器**: 使用训练时保存的MinMaxScaler，不要重新拟合
3. **数据范围**: 确保推理数据包含足够的历史数据（至少60天）
4. **缺失值处理**: 保持一致的缺失值处理策略

### 建议的使用方式:
1. 计算因子时，确保至少有 `max(所有窗口大小) + 10` 天的历史数据
2. 使用增量更新时，保持足够的历史数据缓冲区
3. 定期验证因子计算结果的一致性

## 修复文件
- 主要修复文件: `tushare_data_cyb/calculate_stock_factors_cyb.py`
- 修复日期: 2025-07-16
- 修复内容: 数据一致性问题全面修复
